"use server";
import { prisma } from "@/src/lib/prisma";
import { NextResponse } from "next/server";

export async function POST() {
  try {
    console.log("Iniciando atualização de caminhos de arquivos...");

    // Buscar todos os arquivos que precisam ser limpos
    const filesToUpdate = await prisma.file.findMany({
      where: {
        OR: [
          {
            path: {
              startsWith: "photos/",
            },
          },
          {
            path: {
              startsWith: "thumbnails/",
            },
          },
          {
            path: {
              contains: "matriz/",
            },
          },
          {
            thumbnailPath: {
              startsWith: "thumbnails/",
            },
          },
          {
            thumbnailPath: {
              contains: "matriz/",
            },
          },
        ],
      },
      select: {
        id: true,
        path: true,
        name: true,
        thumbnailPath: true,
      },
    });

    console.log(`Encontrados ${filesToUpdate.length} arquivos para atualizar`);

    if (filesToUpdate.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Nenhum arquivo encontrado para atualizar",
        totalFiles: 0,
        updatedFiles: 0,
        logs: [],
      });
    }

    const logs: Array<{
      fileId: string;
      fileName: string;
      oldPath: string;
      newPath: string;
      status: string;
      oldThumbnailPath?: string | null;
      newThumbnailPath?: string | null;
    }> = [];

    let updatedCount = 0;

    // Função para limpar o caminho
    const cleanPath = (path: string): string => {
      let cleanedPath = path;

      // Remover todas as ocorrências de "matriz/salvador/"
      cleanedPath = cleanedPath.replace(/matriz\/salvador\//g, "");

      // Extrair a parte após todas as duplicações de "matriz/"
      // Isso remove todas as ocorrências de "matriz/" do início
      cleanedPath = cleanedPath.replace(/^(matriz\/)+/g, "");

      // Se o caminho limpo começa com "photos/", garantir que tenha "matriz/" no início
      if (cleanedPath.startsWith("photos/")) {
        cleanedPath = `matriz/${cleanedPath}`;
      }
      // Se o caminho limpo começa com "thumbnails/", garantir que tenha "matriz/" no início
      else if (cleanedPath.startsWith("thumbnails/")) {
        cleanedPath = `matriz/${cleanedPath}`;
      }
      // Se já começar com "matriz/photos/" ou "matriz/thumbnails/", manter como está
      else if (
        cleanedPath.startsWith("matriz/photos/") ||
        cleanedPath.startsWith("matriz/thumbnails/")
      ) {
        // Já está correto, não fazer nada
      }
      // Para outros casos, manter o caminho limpo como está

      return cleanedPath;
    };

    // Atualizar cada arquivo
    for (const file of filesToUpdate) {
      try {
        const newPath = cleanPath(file.path);
        const newThumbnailPath = file.thumbnailPath
          ? cleanPath(file.thumbnailPath)
          : null;

        // Verificar se algum caminho mudou
        const pathChanged = newPath !== file.path;
        const thumbnailChanged = newThumbnailPath !== file.thumbnailPath;

        if (pathChanged || thumbnailChanged) {
          const updateData: { path?: string; thumbnailPath?: string | null } =
            {};

          if (pathChanged) {
            updateData.path = newPath;
          }

          if (thumbnailChanged) {
            updateData.thumbnailPath = newThumbnailPath;
          }

          await prisma.file.update({
            where: { id: file.id },
            data: updateData,
          });

          updatedCount++;
          logs.push({
            fileId: file.id,
            fileName: file.name,
            oldPath: file.path,
            newPath: newPath,
            status: "updated",
            oldThumbnailPath: file.thumbnailPath,
            newThumbnailPath: newThumbnailPath,
          });

          console.log(
            `Arquivo atualizado: ${file.name} - path: ${
              file.path
            } -> ${newPath}${
              thumbnailChanged
                ? `, thumbnail: ${file.thumbnailPath} -> ${newThumbnailPath}`
                : ""
            }`
          );
        } else {
          logs.push({
            fileId: file.id,
            fileName: file.name,
            oldPath: file.path,
            newPath: newPath,
            status: "no-change",
            oldThumbnailPath: file.thumbnailPath,
            newThumbnailPath: newThumbnailPath,
          });
        }
      } catch (error) {
        console.error(`Erro ao atualizar arquivo ${file.id}:`, error);
        logs.push({
          fileId: file.id,
          fileName: file.name,
          oldPath: file.path,
          newPath: cleanPath(file.path),
          status: "error",
          oldThumbnailPath: file.thumbnailPath,
          newThumbnailPath: file.thumbnailPath
            ? cleanPath(file.thumbnailPath)
            : null,
        });
      }
    }

    console.log(
      `Atualização concluída. ${updatedCount} arquivos atualizados de ${filesToUpdate.length} encontrados.`
    );

    return NextResponse.json({
      success: true,
      message: `Atualização concluída com sucesso`,
      totalFiles: filesToUpdate.length,
      updatedFiles: updatedCount,
      logs: logs,
    });
  } catch (error) {
    console.error("Erro durante a atualização de caminhos:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : "Erro desconhecido",
      },
      { status: 500 }
    );
  }
}
