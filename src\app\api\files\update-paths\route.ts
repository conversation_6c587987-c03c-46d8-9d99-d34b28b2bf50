"use server";
import { prisma } from "@/src/lib/prisma";
import { NextResponse } from "next/server";

export async function POST() {
  try {
    console.log("Iniciando atualização de caminhos de arquivos...");

    // Buscar todos os arquivos que precisam ser limpos
    const filesToUpdate = await prisma.file.findMany({
      where: {
        OR: [
          {
            path: {
              startsWith: "photos/",
            },
          },
          {
            path: {
              contains: "matriz/",
            },
          },
        ],
      },
      select: {
        id: true,
        path: true,
        name: true,
      },
    });

    console.log(`Encontrados ${filesToUpdate.length} arquivos para atualizar`);

    if (filesToUpdate.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Nenhum arquivo encontrado para atualizar",
        totalFiles: 0,
        updatedFiles: 0,
        logs: [],
      });
    }

    const logs: Array<{
      fileId: string;
      fileName: string;
      oldPath: string;
      newPath: string;
      status: string;
    }> = [];

    let updatedCount = 0;

    // Função para limpar o caminho
    const cleanPath = (path: string): string => {
      // Remover todas as ocorrências de "matriz/salvador/" e "matriz/" duplicadas
      let cleanedPath = path;

      // Remover "matriz/salvador/" repetidas
      cleanedPath = cleanedPath.replace(/matriz\/salvador\//g, "");

      // Remover "matriz/" repetidas, mas manter uma no início se necessário
      cleanedPath = cleanedPath.replace(/^(matriz\/)+/g, "");

      // Se o caminho começa com "photos/", adicionar "matriz/" no início
      if (cleanedPath.startsWith("photos/")) {
        cleanedPath = `matriz/${cleanedPath}`;
      }

      return cleanedPath;
    };

    // Atualizar cada arquivo
    for (const file of filesToUpdate) {
      try {
        const newPath = cleanPath(file.path);

        // Só atualizar se o caminho realmente mudou
        if (newPath !== file.path) {
          await prisma.file.update({
            where: { id: file.id },
            data: { path: newPath },
          });

          updatedCount++;
          logs.push({
            fileId: file.id,
            fileName: file.name,
            oldPath: file.path,
            newPath: newPath,
            status: "updated",
          });

          console.log(
            `Arquivo atualizado: ${file.name} - ${file.path} -> ${newPath}`
          );
        } else {
          logs.push({
            fileId: file.id,
            fileName: file.name,
            oldPath: file.path,
            newPath: newPath,
            status: "no-change",
          });
        }
      } catch (error) {
        console.error(`Erro ao atualizar arquivo ${file.id}:`, error);
        logs.push({
          fileId: file.id,
          fileName: file.name,
          oldPath: file.path,
          newPath: cleanPath(file.path),
          status: "error",
        });
      }
    }

    console.log(
      `Atualização concluída. ${updatedCount} arquivos atualizados de ${filesToUpdate.length} encontrados.`
    );

    return NextResponse.json({
      success: true,
      message: `Atualização concluída com sucesso`,
      totalFiles: filesToUpdate.length,
      updatedFiles: updatedCount,
      logs: logs,
    });
  } catch (error) {
    console.error("Erro durante a atualização de caminhos:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : "Erro desconhecido",
      },
      { status: 500 }
    );
  }
}
