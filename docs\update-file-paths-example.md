# Atualização de Caminhos de Arquivos

Esta documentação explica como usar a API e action criadas para limpar e normalizar os caminhos dos arquivos na tabela File, removendo duplicações e garantindo que arquivos de fotos tenham apenas o prefixo "matriz/".

## API Endpoint

### POST `/api/files/update-paths`

Limpa e normaliza todos os caminhos de arquivos na tabela File (colunas `path` e `thumbnailPath`), removendo duplicações de "matriz/salvador/" e "matriz/", e garantindo que arquivos de fotos e thumbnails tenham apenas "matriz/" como prefixo.

**Exemplo de uso:**

```javascript
// Cha<PERSON> via fetch
const response = await fetch('/api/files/update-paths', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  }
});

const result = await response.json();
console.log(result);
```

**Resposta de sucesso:**
```json
{
  "success": true,
  "message": "Atualização concluída com sucesso",
  "totalFiles": 15,
  "updatedFiles": 15,
  "logs": [
    {
      "fileId": "clx123...",
      "fileName": "img-20250722-wa0300.jpg",
      "oldPath": "photos/c9cc410e-09f6-4b40-9627-fced9c9f6f9e/img-20250722-wa0300.jpg",
      "newPath": "matriz/photos/c9cc410e-09f6-4b40-9627-fced9c9f6f9e/img-20250722-wa0300.jpg",
      "status": "updated",
      "oldThumbnailPath": "matriz/salvador/thumbnails/thumb-clx123.jpg",
      "newThumbnailPath": "matriz/thumbnails/thumb-clx123.jpg"
    }
  ]
}
```

## Server Action

### `updateFilePathsWithMatrizPrefix()`

Função server action que pode ser chamada diretamente em componentes React ou outras server actions.

**Exemplo de uso:**

```typescript
import { updateFilePathsWithMatrizPrefix } from '@/src/actions/files';

// Em um componente ou server action
const handleUpdatePaths = async () => {
  const result = await updateFilePathsWithMatrizPrefix();
  
  if (result.success) {
    console.log(`${result.updatedFiles} arquivos atualizados de ${result.totalFiles} encontrados`);
    console.log('Logs:', result.logs);
  } else {
    console.error('Erro:', result.message);
  }
};
```

## Exemplos de Transformação

### Caso 1: Arquivo simples com "photos/"

**Antes:**
```text
photos/c9cc410e-09f6-4b40-9627-fced9c9f6f9e/img-20250722-wa0300.jpg
```

**Depois:**
```text
matriz/photos/c9cc410e-09f6-4b40-9627-fced9c9f6f9e/img-20250722-wa0300.jpg
```

### Caso 2: Arquivo com múltiplas duplicações

**Antes:**
```text
matriz/salvador/matriz/salvador/matriz/salvador/matriz/salvador/matriz/photos/d9bd6ab1-1f35-4167-bae4-eb5f11877aa5/2.jpg
```

**Depois:**
```text
matriz/photos/d9bd6ab1-1f35-4167-bae4-eb5f11877aa5/2.jpg
```

### Caso 3: Arquivo com apenas "matriz/" duplicado

**Antes:**
```text
matriz/matriz/matriz/photos/abc123/image.jpg
```

**Depois:**
```text
matriz/photos/abc123/image.jpg
```

### Caso 4: Thumbnail sem prefixo

**Antes:**
```text
thumbnails/cmd3dk0fn0000any9zb02n8nf
```

**Depois:**
```text
matriz/thumbnails/cmd3dk0fn0000any9zb02n8nf
```

## Características

- ✅ Limpa arquivos que começam com "photos/", "thumbnails/" ou contêm "matriz/"
- ✅ Remove todas as duplicações de "matriz/salvador/" e "matriz/"
- ✅ Garante que arquivos de fotos tenham apenas "matriz/" como prefixo
- ✅ **Garante que thumbnails tenham apenas "matriz/" como prefixo**
- ✅ **Aplica limpeza tanto no `path` quanto no `thumbnailPath`**
- ✅ Só atualiza arquivos que realmente precisam de mudança
- ✅ Fornece logs detalhados de cada operação (updated, no-change, error)
- ✅ Inclui informações de thumbnail nos logs para auditoria completa
- ✅ Trata erros individualmente por arquivo
- ✅ Retorna estatísticas completas da operação
- ✅ Disponível tanto como API quanto como server action

## Segurança

- A operação é idempotente (pode ser executada múltiplas vezes sem problemas)
- Apenas arquivos com caminhos específicos são afetados
- Logs detalhados permitem auditoria das mudanças
- Tratamento de erros individual por arquivo
