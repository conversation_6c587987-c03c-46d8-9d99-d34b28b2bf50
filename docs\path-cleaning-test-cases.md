# Casos de Teste para Limpeza de Caminhos

Esta documentação mostra como a função `cleanPath` lida com diferentes cenários de caminhos de arquivos.

## Lógica da Função

```javascript
const cleanPath = (path: string): string => {
  let cleanedPath = path;

  // 1. Remover todas as ocorrências de "matriz/salvador/"
  cleanedPath = cleanedPath.replace(/matriz\/salvador\//g, "");

  // 2. Remover todas as duplicações de "matriz/" do início
  cleanedPath = cleanedPath.replace(/^(matriz\/)+/g, "");

  // 3. Se começa com "photos/", adicionar "matriz/" no início
  if (cleanedPath.startsWith("photos/")) {
    cleanedPath = `matriz/${cleanedPath}`;
  }
  // 4. Se já começa com "matriz/photos/", manter como está
  else if (cleanedPath.startsWith("matriz/photos/")) {
    // J<PERSON> está correto, não fazer nada
  }
  
  return cleanedPath;
};
```

## Casos de Teste

### ✅ Caso 1: Arquivo simples com "photos/"
**Entrada:** `"photos/abc/image.jpg"`
**Processo:**
1. Remove "matriz/salvador/": `"photos/abc/image.jpg"` (sem mudança)
2. Remove "matriz/" do início: `"photos/abc/image.jpg"` (sem mudança)
3. Começa com "photos/": adiciona "matriz/" → `"matriz/photos/abc/image.jpg"`

**Resultado:** `"matriz/photos/abc/image.jpg"`

### ✅ Caso 2: Arquivo já correto
**Entrada:** `"matriz/photos/abc/image.jpg"`
**Processo:**
1. Remove "matriz/salvador/": `"matriz/photos/abc/image.jpg"` (sem mudança)
2. Remove "matriz/" do início: `"photos/abc/image.jpg"`
3. Começa com "photos/": adiciona "matriz/" → `"matriz/photos/abc/image.jpg"`

**Resultado:** `"matriz/photos/abc/image.jpg"` (sem mudança real)

### ✅ Caso 3: Múltiplas duplicações
**Entrada:** `"matriz/salvador/matriz/salvador/matriz/photos/abc/image.jpg"`
**Processo:**
1. Remove "matriz/salvador/": `"matriz/photos/abc/image.jpg"`
2. Remove "matriz/" do início: `"photos/abc/image.jpg"`
3. Começa com "photos/": adiciona "matriz/" → `"matriz/photos/abc/image.jpg"`

**Resultado:** `"matriz/photos/abc/image.jpg"`

### ✅ Caso 4: Apenas "matriz/" duplicado
**Entrada:** `"matriz/matriz/matriz/photos/abc/image.jpg"`
**Processo:**
1. Remove "matriz/salvador/": `"matriz/matriz/matriz/photos/abc/image.jpg"` (sem mudança)
2. Remove "matriz/" do início: `"photos/abc/image.jpg"`
3. Começa com "photos/": adiciona "matriz/" → `"matriz/photos/abc/image.jpg"`

**Resultado:** `"matriz/photos/abc/image.jpg"`

### ✅ Caso 5: Arquivo não relacionado a fotos
**Entrada:** `"matriz/salvador/matriz/documents/file.pdf"`
**Processo:**
1. Remove "matriz/salvador/": `"matriz/documents/file.pdf"`
2. Remove "matriz/" do início: `"documents/file.pdf"`
3. Não começa com "photos/": mantém como está

**Resultado:** `"documents/file.pdf"`

### ✅ Caso 6: Caminho complexo já correto
**Entrada:** `"matriz/photos/2024/janeiro/foto1.jpg"`
**Processo:**
1. Remove "matriz/salvador/": `"matriz/photos/2024/janeiro/foto1.jpg"` (sem mudança)
2. Remove "matriz/" do início: `"photos/2024/janeiro/foto1.jpg"`
3. Começa com "photos/": adiciona "matriz/" → `"matriz/photos/2024/janeiro/foto1.jpg"`

**Resultado:** `"matriz/photos/2024/janeiro/foto1.jpg"` (sem mudança real)

### ✅ Caso 7: Arquivo com thumbnailPath duplicado
**Entrada:**
- `path`: `"matriz/photos/abc/image.jpg"`
- `thumbnailPath`: `"matriz/salvador/matriz/thumbnails/thumb-abc.jpg"`

**Processo:**
- `path`: Já correto, sem mudança
- `thumbnailPath`: `"matriz/salvador/matriz/thumbnails/thumb-abc.jpg"` → `"matriz/thumbnails/thumb-abc.jpg"`

**Resultado:**
- `path`: `"matriz/photos/abc/image.jpg"` (sem mudança)
- `thumbnailPath`: `"matriz/thumbnails/thumb-abc.jpg"` (atualizado)
- **Status**: `"updated"` (porque thumbnailPath mudou)

## Resumo

A função é **idempotente** - pode ser executada múltiplas vezes no mesmo arquivo sem causar problemas:

- ✅ **Arquivos já corretos** (`matriz/photos/...`) permanecem inalterados
- ✅ **Arquivos com duplicações** são limpos corretamente
- ✅ **Arquivos simples** (`photos/...`) recebem o prefixo correto
- ✅ **Arquivos não relacionados** a fotos são limpos mas mantêm sua estrutura
- ✅ **ThumbnailPath** também é limpo com a mesma lógica

## Otimização

A função só atualiza o banco de dados se `newPath !== file.path` **OU** `newThumbnailPath !== file.thumbnailPath`, evitando UPDATEs desnecessários para arquivos já corretos.
