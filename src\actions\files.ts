"use server";
import { prisma } from "@/src/lib/prisma";
import { storageProvider } from "@/src/lib/storage";
import { parseObject, toKebabCase } from "@/src/lib/utils";
import { File } from "@/src/types/core/file";
import { generateAndStoreThumbnail } from "@/src/lib/thumbnail";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function loadFile(path: string) {
  try {
    return await storageProvider.get(path);
  } catch (error) {
    console.error(error);
  }
}

export async function saveFile(
  file: File & { thumbnailBuffer?: number[]; thumbnailType?: string }
) {
  try {
    console.log(
      `Iniciando upload do arquivo: ${file.name}, caminho: ${file.path}`
    );
    const url = await storageProvider.upload(file);
    console.log(`Upload concluído, URL gerada: ${url}`);

    const createdFile = await prisma.file.create({
      data: {
        name: file.name,
        path: url,
        type: file.type,
        size: file.size,
      },
    });

    console.log(`Arquivo salvo no banco de dados com ID: ${createdFile.id}`);

    // Gerar miniatura para imagens
    if (file.type.startsWith("image/")) {
      const thumbnailPath = await generateAndStoreThumbnail(
        createdFile.id,
        url
      );
      if (thumbnailPath) {
        // Atualizar o registro com o caminho da miniatura
        await prisma.file.update({
          where: { id: createdFile.id },
          data: { thumbnailPath },
        });
        console.log(`Miniatura gerada e salva: ${thumbnailPath}`);
      }
    }

    // Salvar thumbnail de vídeo enviada pelo front-end
    if (
      file.type.startsWith("video/") &&
      file.thumbnailBuffer &&
      file.thumbnailType
    ) {
      console.log("[THUMBNAIL] Recebendo thumbnail de vídeo do front-end:");
      console.log("[THUMBNAIL] fileId:", createdFile.id);
      console.log(
        "[THUMBNAIL] thumbnailBuffer length:",
        file.thumbnailBuffer.length
      );
      console.log("[THUMBNAIL] thumbnailType:", file.thumbnailType);
      // Obter informações da organização para o caminho
      const { organizationName } = await getCurrentOrganization();
      if (!organizationName) {
        throw new Error(
          "Nome da organização não encontrado para salvar thumbnail."
        );
      }
      const orgSlug = toKebabCase(organizationName);

      const thumbnailFileName = `thumb-${createdFile.id}.jpg`;
      const thumbnailPath = `${orgSlug}/thumbnails/${thumbnailFileName}`;
      await storageProvider.upload({
        id: "", // id fictício, não é usado no upload
        name: thumbnailFileName,
        path: `${orgSlug}/thumbnails`, // incluir organização no caminho
        type: file.thumbnailType,
        size: file.thumbnailBuffer.length,
        buffer: file.thumbnailBuffer,
      } as File);
      const updateResult = await prisma.file.update({
        where: { id: createdFile.id },
        data: { thumbnailPath },
      });
      console.log(`[THUMBNAIL] Thumbnail de vídeo salva: ${thumbnailPath}`);
      console.log("[THUMBNAIL] Registro atualizado:", updateResult);
    }

    return parseObject(createdFile) as File;
  } catch (error) {
    console.error("Erro ao salvar arquivo:", error);
    throw new Error("Erro ao salvar arquivo");
  }
}

export async function saveFiles(
  files: (File & { thumbnailBuffer?: number[]; thumbnailType?: string })[]
) {
  try {
    const urls = await storageProvider.uploadFiles(files);

    files.forEach((file, index) => {
      file.path = urls[index];
    });

    await prisma.file.createMany({
      data: files.map((file) => ({
        name: file.name,
        path: file.path,
        type: file.type,
        size: file.size,
      })),
    });

    const createdFiles = await prisma.file.findMany({
      where: {
        path: {
          in: files.map((file) => file.path),
        },
      },
    });

    // Gerar miniaturas para imagens
    const imageFiles = createdFiles.filter((file) =>
      file.type.startsWith("image/")
    );
    if (imageFiles.length > 0) {
      const thumbnailPromises = imageFiles.map((file) =>
        generateAndStoreThumbnail(file.id, file.path)
      );
      await Promise.all(thumbnailPromises);
      console.log(`Miniaturas geradas para ${imageFiles.length} imagens`);
    }

    // Salvar thumbnails de vídeos enviados pelo front-end
    const videoFilesWithThumb = files.filter(
      (file) =>
        file.type.startsWith("video/") &&
        file.thumbnailBuffer &&
        file.thumbnailType
    );
    if (videoFilesWithThumb.length > 0) {
      for (const file of videoFilesWithThumb) {
        const created = createdFiles.find(
          (f) =>
            f.name === file.name && f.type === file.type && f.size === file.size
        );
        if (created) {
          console.log(
            "[THUMBNAIL] Recebendo thumbnail de vídeo do front-end (saveFiles):"
          );
          console.log("[THUMBNAIL] fileId:", created.id);
          console.log(
            "[THUMBNAIL] thumbnailBuffer length:",
            file.thumbnailBuffer!.length
          );
          console.log("[THUMBNAIL] thumbnailType:", file.thumbnailType!);
          // Obter informações da organização para o caminho
          const { organizationName } = await getCurrentOrganization();
          if (!organizationName) {
            throw new Error(
              "Nome da organização não encontrado para salvar thumbnail."
            );
          }
          const orgSlug = toKebabCase(organizationName);

          const thumbnailFileName = `thumb-${created.id}.jpg`;
          const thumbnailPath = `${orgSlug}/thumbnails/${thumbnailFileName}`;
          await storageProvider.upload({
            id: "",
            name: thumbnailFileName,
            path: `${orgSlug}/thumbnails`, // incluir organização no caminho
            type: file.thumbnailType!,
            size: file.thumbnailBuffer!.length,
            buffer: file.thumbnailBuffer!,
          } as File);
          const updateResult = await prisma.file.update({
            where: { id: created.id },
            data: { thumbnailPath },
          });
          console.log(`[THUMBNAIL] Thumbnail de vídeo salva: ${thumbnailPath}`);
          console.log("[THUMBNAIL] Registro atualizado:", updateResult);
        }
      }
    }

    return parseObject(createdFiles) as File[];
  } catch (error) {
    console.error(error);
  }
}

export async function removeFile(file: File) {
  try {
    // Verificar se o arquivo é válido
    if (!file || !file.id || !file.path) {
      console.log("Arquivo inválido para remover");
      return { success: true, message: "Arquivo inválido para remover" };
    }

    // Buscar informações completas do arquivo
    const fileRecord = await prisma.file.findUnique({
      where: { id: file.id },
    });

    // Remover arquivo do storage
    await storageProvider.delete(file.path);

    // Remover miniatura se existir
    if (fileRecord?.thumbnailPath) {
      try {
        await storageProvider.delete(fileRecord.thumbnailPath);
        console.log(
          `[THUMBNAIL] Miniatura removida: ${fileRecord.thumbnailPath}`
        );
      } catch (thumbnailError) {
        console.error("Erro ao remover miniatura:", thumbnailError);
      }
    }

    // Remover registro do banco de dados
    await prisma.file.delete({ where: { id: file.id } });

    return { success: true, message: "Arquivo removido com sucesso" };
  } catch (error) {
    console.error("Erro ao remover arquivo:", error);
    return { success: false, message: "Erro ao remover arquivo", error };
  }
}

export async function removeFiles(files: File[]) {
  try {
    // Verificar se há arquivos para remover
    if (!files || files.length === 0) {
      console.log("Nenhum arquivo para remover");
      return { success: true, message: "Nenhum arquivo para remover" };
    }

    // Filtrar arquivos válidos (com id e path)
    const validFiles = files.filter((file) => file && file.id && file.path);

    if (validFiles.length === 0) {
      console.log("Nenhum arquivo válido para remover");
      return { success: true, message: "Nenhum arquivo válido para remover" };
    }

    // Buscar informações completas dos arquivos para obter caminhos das miniaturas
    const fileRecords = await prisma.file.findMany({
      where: { id: { in: validFiles.map((file) => file.id) } },
    });

    // Remover arquivos do storage
    await Promise.all(
      validFiles.map((file) => storageProvider.delete(file.path))
    );

    // Remover miniaturas se existirem
    for (const fileRecord of fileRecords) {
      if (fileRecord.thumbnailPath) {
        try {
          await storageProvider.delete(fileRecord.thumbnailPath);
          console.log(
            `[THUMBNAIL] Miniatura removida: ${fileRecord.thumbnailPath}`
          );
        } catch (thumbnailError) {
          console.error("Erro ao remover miniatura:", thumbnailError);
        }
      }
    }

    // Remover registros do banco de dados
    await prisma.file.deleteMany({
      where: { id: { in: validFiles.map((file) => file.id) } },
    });

    return { success: true, message: "Arquivos removidos com sucesso" };
  } catch (error) {
    console.error("Erro ao remover arquivos:", error);
    return { success: false, message: "Erro ao remover arquivos", error };
  }
}

export async function updateFilePathsWithMatrizPrefix() {
  try {
    console.log("Iniciando atualização de caminhos de arquivos...");

    // Buscar todos os arquivos que precisam ser limpos
    const filesToUpdate = await prisma.file.findMany({
      where: {
        OR: [
          {
            path: {
              startsWith: "photos/",
            },
          },
          {
            path: {
              contains: "matriz/",
            },
          },
        ],
      },
      select: {
        id: true,
        path: true,
        name: true,
      },
    });

    console.log(`Encontrados ${filesToUpdate.length} arquivos para atualizar`);

    if (filesToUpdate.length === 0) {
      return {
        success: true,
        message: "Nenhum arquivo encontrado para atualizar",
        totalFiles: 0,
        updatedFiles: 0,
        logs: [],
      };
    }

    const logs: Array<{
      fileId: string;
      fileName: string;
      oldPath: string;
      newPath: string;
      status: string;
    }> = [];

    let updatedCount = 0;

    // Função para limpar o caminho
    const cleanPath = (path: string): string => {
      // Remover todas as ocorrências de "matriz/salvador/" e "matriz/" duplicadas
      let cleanedPath = path;

      // Remover "matriz/salvador/" repetidas
      cleanedPath = cleanedPath.replace(/matriz\/salvador\//g, "");

      // Remover "matriz/" repetidas, mas manter uma no início se necessário
      cleanedPath = cleanedPath.replace(/^(matriz\/)+/g, "");

      // Se o caminho começa com "photos/", adicionar "matriz/" no início
      if (cleanedPath.startsWith("photos/")) {
        cleanedPath = `matriz/${cleanedPath}`;
      }

      return cleanedPath;
    };

    // Atualizar cada arquivo
    for (const file of filesToUpdate) {
      try {
        const newPath = cleanPath(file.path);

        // Só atualizar se o caminho realmente mudou
        if (newPath !== file.path) {
          await prisma.file.update({
            where: { id: file.id },
            data: { path: newPath },
          });

          updatedCount++;
          logs.push({
            fileId: file.id,
            fileName: file.name,
            oldPath: file.path,
            newPath: newPath,
            status: "updated",
          });

          console.log(
            `Arquivo atualizado: ${file.name} - ${file.path} -> ${newPath}`
          );
        } else {
          logs.push({
            fileId: file.id,
            fileName: file.name,
            oldPath: file.path,
            newPath: newPath,
            status: "no-change",
          });
        }
      } catch (error) {
        console.error(`Erro ao atualizar arquivo ${file.id}:`, error);
        logs.push({
          fileId: file.id,
          fileName: file.name,
          oldPath: file.path,
          newPath: cleanPath(file.path),
          status: "error",
        });
      }
    }

    console.log(
      `Atualização concluída. ${updatedCount} arquivos atualizados de ${filesToUpdate.length} encontrados.`
    );

    return {
      success: true,
      message: `Atualização concluída com sucesso`,
      totalFiles: filesToUpdate.length,
      updatedFiles: updatedCount,
      logs: logs,
    };
  } catch (error) {
    console.error("Erro durante a atualização de caminhos:", error);
    return {
      success: false,
      message: "Erro ao atualizar caminhos dos arquivos",
      error: error instanceof Error ? error.message : "Erro desconhecido",
    };
  }
}
